<?php
/**
 * PFG Core Installation History Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$installations = $this->getInstallations();
$ajaxUrls = $this->getAjaxUrls();
$formKey = $this->getFormKey();
?>

<div id="pfg-core-installation-history" style="margin-top: 10px;">
    <div style="margin-bottom: 15px;">
        <button type="button" onclick="pfgCoreRefreshInstallationHistory()" class="scalable">
            <span><span><span><?php echo $this->__('Refresh History') ?></span></span></span>
        </button>
    </div>

    <div id="pfg-core-installation-loading" style="text-align: center; padding: 20px; display: none;">
        <img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo $this->__('Loading...') ?>" />
        <br />
        <?php echo $this->__('Loading installation history...') ?>
    </div>

    <div id="pfg-core-installation-content">
        <?php if ($installations->getSize() == 0): ?>
            <div class="notice-msg">
                <ul>
                    <li>
                        <span><?php echo $this->__('No installation history found.') ?></span>
                    </li>
                </ul>
            </div>
        <?php else: ?>
        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; background: #f9f9f9;">
            <table cellspacing="0" cellpadding="5" style="width: 100%; background: white;">
                <thead>
                    <tr style="background: #f0f0f0;">
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Module') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Type') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Status') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Version') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Date') ?></th>
                        <th style="text-align: left; padding: 8px; border-bottom: 1px solid #ddd;"><?php echo $this->__('Actions') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($installations as $installation): ?>
                        <tr style="border-bottom: 1px solid #eee;">
                            <td style="padding: 8px;">
                                <strong><?php echo $this->escapeHtml($installation->getModuleName()) ?></strong>
                                <?php if ($installation->getRepositoryName()): ?>
                                    <br><small style="color: #666;"><?php echo $this->escapeHtml($installation->getRepositoryName()) ?></small>
                                <?php endif; ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php
                                $typeLabels = array(
                                    PFG_Core_Model_Installation::TYPE_INSTALL => $this->__('Install'),
                                    PFG_Core_Model_Installation::TYPE_UPDATE => $this->__('Update'),
                                    PFG_Core_Model_Installation::TYPE_ROLLBACK => $this->__('Rollback')
                                );
                                echo isset($typeLabels[$installation->getInstallationType()]) 
                                    ? $typeLabels[$installation->getInstallationType()] 
                                    : $installation->getInstallationType();
                                ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php
                                $status = $installation->getInstallationStatus();
                                $colors = array(
                                    PFG_Core_Model_Installation::STATUS_PENDING => '#f18500',
                                    PFG_Core_Model_Installation::STATUS_IN_PROGRESS => '#2f55d4',
                                    PFG_Core_Model_Installation::STATUS_COMPLETED => '#3d6611',
                                    PFG_Core_Model_Installation::STATUS_FAILED => '#df280a',
                                    PFG_Core_Model_Installation::STATUS_ROLLED_BACK => '#eb5202',
                                );
                                
                                $labels = array(
                                    PFG_Core_Model_Installation::STATUS_PENDING => $this->__('Pending'),
                                    PFG_Core_Model_Installation::STATUS_IN_PROGRESS => $this->__('In Progress'),
                                    PFG_Core_Model_Installation::STATUS_COMPLETED => $this->__('Completed'),
                                    PFG_Core_Model_Installation::STATUS_FAILED => $this->__('Failed'),
                                    PFG_Core_Model_Installation::STATUS_ROLLED_BACK => $this->__('Rolled Back'),
                                );
                                
                                $color = isset($colors[$status]) ? $colors[$status] : '#666';
                                $label = isset($labels[$status]) ? $labels[$status] : $status;
                                ?>
                                <span style="color: <?php echo $color ?>; font-weight: bold; font-size: 11px;">
                                    <?php echo $this->escapeHtml($label) ?>
                                </span>
                            </td>
                            <td style="padding: 8px;">
                                <?php echo $this->escapeHtml($installation->getVersionInstalled() ?: '-') ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php echo $this->formatDate($installation->getCreatedAt(), 'short', true) ?>
                            </td>
                            <td style="padding: 8px;">
                                <?php if ($installation->getBackupId() && 
                                         $installation->getInstallationStatus() == PFG_Core_Model_Installation::STATUS_COMPLETED): ?>
                                    <button type="button" 
                                            onclick="pfgCoreRollbackInstallation(<?php echo $installation->getId() ?>)" 
                                            class="scalable" 
                                            style="font-size: 11px;"
                                            title="<?php echo $this->__('Rollback this installation') ?>">
                                        <span><span><span><?php echo $this->__('Rollback') ?></span></span></span>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 10px; text-align: right;">
            <small style="color: #666;">
                <?php echo $this->__('Showing last %d installations', $installations->getSize()) ?>
            </small>
        </div>
        <?php endif; ?>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
function pfgCoreRefreshInstallationHistory() {
    $('pfg-core-installation-loading').show();
    $('pfg-core-installation-content').hide();

    new Ajax.Request('<?php echo Mage::helper('adminhtml')->getUrl('adminhtml/pfg_core/getInstallationHistory') ?>', {
        method: 'post',
        parameters: {
            'form_key': '<?php echo $formKey ?>'
        },
        onSuccess: function(response) {
            try {
                // Debug: log the raw response
                console.log('Raw response:', response.responseText);
                console.log('Response status:', response.status);
                console.log('Response headers:', response.getAllResponseHeaders());

                var result = response.responseText.evalJSON();

                if (result.success) {
                    $('pfg-core-installation-content').innerHTML = result.html;
                } else {
                    alert('<?php echo $this->__('Failed to refresh installation history: ') ?>' + (result.error || '<?php echo $this->__('Unknown error') ?>'));
                }
            } catch (e) {
                console.log('JSON parse error:', e);
                console.log('Response text:', response.responseText);
                console.log('Response length:', response.responseText.length);
                alert('<?php echo $this->__('Invalid response from server. Check console for details.') ?>');
            }
        },
        onFailure: function() {
            alert('<?php echo $this->__('Failed to refresh installation history') ?>');
        },
        onComplete: function() {
            $('pfg-core-installation-loading').hide();
            $('pfg-core-installation-content').show();
        }
    });
}

function pfgCoreRollbackInstallation(installationId) {
    if (!confirm('<?php echo $this->__('Are you sure you want to rollback this installation? This will restore files from the backup.') ?>')) {
        return;
    }
    
    var button = event.target;
    var originalText = button.innerHTML;
    button.innerHTML = '<span><span><span><?php echo $this->__('Rolling back...') ?></span></span></span>';
    button.disabled = true;
    
    new Ajax.Request('<?php echo $ajaxUrls['rollback'] ?>', {
        method: 'post',
        parameters: {
            'form_key': '<?php echo $formKey ?>',
            'installation_id': installationId
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                if (result.success) {
                    alert('<?php echo $this->__('Rollback completed successfully') ?>');
                    // Refresh the installation history
                    pfgCoreRefreshInstallationHistory();
                } else {
                    alert('<?php echo $this->__('Rollback failed: ') ?>' + (result.error || '<?php echo $this->__('Unknown error') ?>'));
                }
            } catch (e) {
                alert('<?php echo $this->__('Invalid response from server') ?>');
            }
        },
        onFailure: function() {
            alert('<?php echo $this->__('Rollback request failed') ?>');
        },
        onComplete: function() {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    });
}
//]]>
</script>
